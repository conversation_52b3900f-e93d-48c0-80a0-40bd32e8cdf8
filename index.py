MYSERVER_ID = "dujRgg47eLS4kAk"
# TODO:
# FIXME:
# + Add Part Algorithm to addFile if the file still big after compressed
# + Add Salting on password hass
# + Set DATA-SYNC = true
# + Enable Kaffeine
# + Remember to set SIC = True be4 deploy
# + Recommend to set SB = True (Session backup)
# + Set protocol = "https://"
# VamPy
# VamPy v2 
# > Useful ultilities
# PROTOCOL = "http://" # For local
PROTOCOL = "https://" # For public
BACKUP = {"INBACKUP":False,"NEEDBACKUP":False}
BACKUP2 = {"INBACKUP":False,"NEEDBACKUP":False}
import sys
from re import match
from hashlib import sha256
from time import sleep,time
from sanic_cors import CORS
from base64 import b64decode
from threading import Thread
from smtplib import SMTP_SSL
from datetime import datetime
from os import getcwd,listdir
from bisect import bisect_right
from io import BytesIO,<PERSON><PERSON>
from traceback import format_exc
from random import choice,randint
from requests import get as rqget
from json import loads,dumps,dump
from sanic import Sanic, response
from lzma import decompress,compress
from sanic_jinja2 import SanicJinja2
from openpyxl import load_workbook
from contextlib import contextmanager
from cryptography.fernet import Fernet
from email.message import EmailMessage
from datetime import datetime,timedelta
from os.path import isfile,isdir,exists
from string import ascii_letters,digits
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.http import MediaIoBaseDownload,MediaIoBaseUpload
python_version = f"{sys.version_info[0]}.{sys.version_info[1]}.{sys.version_info[2]}"
print("BPlanv2 - Running from Python version : "+python_version)
def multi_thread(func):
    """
    Decorator that multithreads the target function
    with the given parameters. Returns the thread
    created for the function
    """
    def wrapper(*args, **kwargs):
        thread = Thread(target=func, args=args)
        thread.start()
        return thread
    return wrapper

 



# Library Import
# from werkzeug.middleware.profiler import ProfilerMiddleware
def getCurrentTime():
    naive_utc_dt = datetime.utcnow()
    naive_utc_dt += timedelta(hours=7)
    return (naive_utc_dt)
def rm(str):
    out = []
    for x in str:
        if not x == "" and not x.strip() == "":
            out.append(x.strip())
    return out

# Marker corefunction


def time_str_to_obj(time_str):
    return datetime.strptime(time_str, "%H:%M").time()

def get_current_marker(times_list, target_time_str):
    if not times_list:
        return None
    
    # Convert all times to datetime.time objects
    times = [time_str_to_obj(t) for t in times_list]
    target_time = time_str_to_obj(target_time_str)

    # Sort times
    sorted_times = sorted(times)
    index = bisect_right(sorted_times, target_time)

    if index == 0:
        return sorted_times[0]  # All times are after the target

    return sorted_times[index - 1].strftime("%H:%M")


## SO"error.html"i,error="Something a"b


# SIC - Session Ip Checker
USE_SIC = False #
# SB - Session backup
SB = True
# DS - DataSync
DS = True


# OpenRouter
# Waitress Console Fix
# logging.basicConfig(level=logging.DEBUG, format='%(asctime)s %(message)s')



# Fastload
def load(file,mode="r",typ="p"):
    # "p" : Plain text
    # "j" : Json object
    with open(file,mode,encoding="utf-8") as f:
        data = f.read()
    if typ == "j":
        data = loads(data)
    return data
# LangPack v4.1
def load_language(lang):
    return load(f"./data/langpack/{lang}.json",typ="j")

# SmartInit v2
def sinit(file,obj,typ="j"):
    if exists(file):
        with open(file,"r") as f:
            if typ == "j":
                return loads(f.read())
            else:
                return f.read()
    if typ == "j":
        dump(obj,open(file,"w"))
    else:
        with open(file,"w") as f:
            f.write(obj)
    return obj
def getFromPath(dictionary, path,getParent=False):
    curr = dictionary
    path = path.replace("/","_0_tgt")
    path = path.split("_0_tgt") # Gets rid of '#' as it's uneccessary 
    if getParent and len(path) >= 1:
        if path == 1: return dictionary
        else:path = path[:-1]
    while(len(path)):
        key = path.pop(0)
        curr = curr.get(key)
        if (type(curr) is not dict and len(path)):
            print("Path does not exist!")
            return None 
    return curr

allow = [".","*","_"," "]
def clean(string,allow=allow):
    for i in allow:
        string = string.replace(i,'')
    if string == "":
        string = "empty"
    return string
def safestring(string):
    if not string == "":
        if type(string) == list:
            for i in string:
                if not (clean(i)).isalnum():return False
            return True
        else:
            return (clean(string)).isalnum()
    else:
        return False
# Baze

# FLog'in
## Data-Init
USRDATA = {
    "language":"vi"
}
HOSPITAL = {}

USRDATA = sinit("./data/userdata/settings.json",USRDATA)
LANG = load_language(USRDATA["language"])

## Flog'in
# Flog - A login system for IMed by DDat
# 6.0a - build 020624




# Init dir
MAINPATH = getcwd()


# Fullsub (Don't edit it)
VERIFYLINK = None
HOSTLINK = None

# Important fernet key, never repace,remove it
KEY = b'vpP1z542uqNhm78_WU5KCRXqAN8TjPZ4298fOXg-2Kw='
# Important 
EMAIL = '<EMAIL>'  # The email you setup to send the email using app password
PASSWORD = 'oocu xdgm evtj tagi'  # The app password you generated
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'} # Allowed extension on FILE-UPLOADING
FULLPERMISSION = ["modify_hospital_info","is_admin","is_doctor"]
FULLPAGES = ["dashboard","tables","infomodify","hospital"]
PAGES = ["dashboard","patient"]

TRUSTEDMAIL = ["gmail.com","outlook.com","yahoo.com"]


ALLOWEDSTRING = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********!@#$%^&*_"
ALLOWEDHOSPITALNAME = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********@&*_ "
CONQUEST = list("<>?")
ALLOWEDIP = "**********."
# User construct
# usrname ; addition

## Encryption

# For-fernet
fernet = Fernet(KEY)


# For-Email
app = Sanic(__name__)
EMAIL = '<EMAIL>'  # The email you setup to send the email using app password
PASSWORD = 'sqqt jtju dgtf qohw'  # The app password you generated
sent_from = EMAIL
with open("verifymail.html","r",encoding="utf-8") as f:
    message = f.read()
message = message.replace("##BUTTONTEXT##",LANG["veremail.verify"])
message = message.replace("##ACTION##",LANG["veremail.verify_by_imed"])
email = EmailMessage()
email["From"] = sent_from
email["Subject"] = LANG["veremail.subject"]
# Func
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[-1].lower() in ALLOWED_EXTENSIONS
def checkstring(string:str,allow=ALLOWEDSTRING,checkSpace=True):
    if string == "":
        if checkSpace:
            return False
        else:
            return True

    for i in string:
        if not i in allow:
            return False
    return True
def cleardc(dicte,allow=CONQUEST):
    for z in allow:
        dicte = dicte.replace(z,"")
    return dicte
def cleardict(dictx):
    for g in dictx:
        dictx[g] = cleardc(dictx[g])
    return dictx
def randomstring(num):
    return ''.join(choice(ascii_letters + digits) for _ in range(num))
# Class

# FS
class GoogleDriveAttachment():
    def __init__(self):
        SCOPES = ["https://www.googleapis.com/auth/drive.file"]
        creds = None
        if exists("token.json"):
            creds = Credentials.from_authorized_user_file("token.json", SCOPES)
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                "credentials.json", SCOPES
            )
                creds = flow.run_local_server(port=0)
            # with open("token.json", "w") as token:
                # token.write(creds.to_json())
        try:
            self.service = build("drive", "v3", credentials=creds)
        except HttpError as error:
            # TODO(developer) - Handle errors from drive API.
            print(f"An error occurred: {error}")
    def download_file(self,fd):
        request = self.service.files().get_media(fileId=fd)
        fh = BytesIO()
        downloader = MediaIoBaseDownload(fh, request)
        done = False
        while done is False:
            status, done = downloader.next_chunk()
            print("Download %d%%." % int(status.progress() * 100))
        return fh.getvalue()
    def download_file_byname(self,file,directories_id=None):
        results = (
            self.service.files()
            .list(pageSize=10, fields="nextPageToken, files(id, name, mimeType,parents)")
            .execute()
        )
        items = results.get("files", [])
        r = None
        for item in items:
            if item["name"] == file:
                print(directories_id,item["parents"])
                if directories_id:
                    if item["parents"][-1] == directories_id:
                        r = item["id"]
                        break
                else:
                    r = item["id"]
                    break
        if not r:return None
        return self.download_file(r)
    def upload_file(self, filename, mimetype, ud_filename,parents,makePublic=False,resumable=True, chunksize=262144):
        if parents == None:
            pass
        elif parents[0] == "name":
            parents = self.create_folder(parents[1])
        else:
            parents = parents[1]
        media = MediaIoBaseUpload(filename, mimetype=mimetype, resumable=resumable, chunksize=chunksize)
        existsrel = False
        r = None
        results = (
            self.service.files()
            .list(pageSize=10, fields="nextPageToken, files(id, name, mimeType,parents)")
            .execute()
        )
        items = results.get("files", [])
        for item in items:
            if item["name"] == ud_filename and item["parents"][-1] == parents:
                existsrel = True
                r= item["id"]
        body = {"name": ud_filename}
        if existsrel:
            request = self.service.files().update(fileId=r,body=body, media_body=media).execute()
        else:
            if parents:
                body["parents"]= [parents]
            request = self.service.files().create(body=body, media_body=media).execute()

        # Return file id
        fid = request["id"]
        if makePublic:
            permission = {
                "type": "anyone",
                "role": "reader"
            }
            self.service.permissions().create(
                fileId=fid,
                body=permission
            ).execute()
        return fid
        # https://drive.google.com/uc?export=download&id=1KODJieuitjJKp4NgEqKRyX9BGJ_IpX1e
    # create a function for creating folders, continue if the folders exists
    def create_folder(self, folder_name):
        results = (
            self.service.files()
            .list(pageSize=10, fields="nextPageToken, files(id, name, mimeType)")
            .execute()
        )
        items = results.get("files", [])
        for item in items:
            if item["name"] == folder_name and item["mimeType"] == "application/vnd.google-apps.folder":
                return item["id"]
                
        file_metadata = {
            "name": folder_name,
            "mimeType": "application/vnd.google-apps.folder",
        }
        file = (
            self.service.files()
            .create(body=file_metadata, fields="id")
            .execute()
        )
        # print("Folder ID: %s" % file.get("id"))
        return file.get("id")

class FileSystem():
    def __init__(self,file,obj,typef="j",DS=DS):
        self.file = file
        if not DS:
            if exists(file):
                with open(file,"rb") as f:
                    r = decompress(f.read())
                    if typef == "j":
                        self.data = loads(r)
                    else:
                        self.data = r
            else:
                if type(obj) == dict:
                    objx = dumps(obj).encode("utf-8")
                else:
                    objx = obj.encode("utf-8")
                objr = compress(objx)
                with open(file,"wb") as f:
                    f.write(objr)
                self.data = obj
        else:
            data = gdapi.download_file_byname(file,MAIN_DIRECTORIES)
            if data == None:
                self.data = obj
            else:
                if typef == "j":
                    self.data = loads(decompress(data).decode("utf-8"))
                else:
                    self.data = decompress(data).decode("utf-8")
    def save(self):
        if type(self.data) == dict:
            tmp = dumps(self.data).encode("utf-8")
        else:
            tmp = self.data.encode("utf-8")
        obj = compress(tmp)
        if not DS:
            with open(self.file,"wb") as f:
                f.write(obj)
        else:
            gdapi.upload_file(BytesIO(obj),"application/x-xz",self.file,("id",MAIN_DIRECTORIES))


# Check first run
# if DS and not exists("./data/bpl/firststart"):
#     with open("./data/bpl/firststart","w") as f:
#         f.write("")
#     sinit("./data/bpl/baze.xz",fserv.getFile("/htdocs/currit/baze.xz",rollBack=True))
#     sinit("./data/bpl/bpl.xz",fserv.getFile("/htdocs/currit/bpl.xz",rollBack=True))
#     sinit("./data/bpl/session.xz",fserv.getFile("/htdocs/currit/session.xz",rollBack=True))


def sendmailfx(usr,code,emailr):
    global VERIFYLINK
    tecx = usr+"|"+code+"|"+emailr
    tecx = fernet.encrypt(tecx.encode("utf-8")).decode("utf-8")




    print("Login!")
    while True:
        try:
            smtpserver = SMTP_SSL('smtp.gmail.com', 465,timeout=10)
            smtpserver.ehlo()
            smtpserver.login(EMAIL, PASSWORD)
            print("Sending!")
            emsi = email
            message_r = message
            message_r = message_r.replace("##LINK##",VERIFYLINK+f"?q={tecx}")
            message_r = message_r.replace("##USER##",usr)
            emsi.set_content(message_r, subtype="html")
            print("Email set!")

            smtpserver.sendmail(sent_from, emailr, emsi.as_string())

            # Close the connection
            smtpserver.close()
            print("Sent!")
            break
        except:
            print("Tried")
            pass
def sendmail(usr,code,email):
    Thread(target=sendmailfx,args=(usr,code,email),daemon=True).start()
PASSCODE = "jK4yBt8ANu06eraqhy7tkg2pgRR2Lsp1"
def vlink(request):
    global VERIFYLINK,HOSTLINK
    if not VERIFYLINK:
        FLEx(request,app,PASSCODE)
        VERIFYLINK =PROTOCOL + request.host + request.app.url_for("vmail")
        HOSTLINK = PROTOCOL + request.host
class Flog():
    def __init__(self) -> None:
        global SB
        # Init login file
        self.session = {}
        self.hospital_obj = FileSystem("bpl.xz",{
                "info":{"address":"",
                         "number":"",
                         "user":{}
                         },
                "datapkg":{
                    "used_email":[]
                }
            })
        self.hospital = self.hospital_obj.data
    @multi_thread
    def backup(self) -> None:
        if not BACKUP["INBACKUP"]:
            BACKUP["INBACKUP"] = True
            print("[FlogBackup] Starting")
            self.hospital_obj.save()
            print("[FlogBackup] Done, wait 65sec for next backup")
            sleep(65)
            BACKUP["INBACKUP"] = False
            if BACKUP["NEEDBACKUP"]:
                print("[FlogBackup] Backup In_queue")
                self.backup()
                BACKUP["NEEDBACKUP"] = False
        else:
            BACKUP["NEEDBACKUP"] = True
    def register(self,usr:str,password:str,permission:list,email=None,addition:dict={}) -> bool:
        # Check err
        if usr in self.hospital["info"]["user"]:
            return (False,LANG["flog.error.already_signed_ac"])

        if not checkstring(usr) or not checkstring(password):
            return (False,LANG["flog.error.invalid_name"])

        # Sha256 password
        password_sha256 = (sha256(password.encode('utf-8')).hexdigest())


        if email:
            a = self.checkmail(email)
            if not a[0]:
                return (False,a[1])
            else:
                codeverify = ''.join(choice(ascii_letters + digits) for _ in range(6))
                out = {"email":email,"verified":False,"codeverify":codeverify,"created":time()}
                self.hospital["datapkg"]["used_email"].append(email)
        else:
            out = {}
        self.hospital["info"]["user"][usr] = {"password":password_sha256,"permission":permission,"datapkg":out,"addition":addition}
        self.backup()
        if not email:
            return (True,LANG["flog.info.register_success"])
        else:
            sendmail(usr,codeverify,email)
            return (codeverify,LANG["flog.info.confirm_email"])
    def verify(self,usr,email,code):
        if not "codeverify" in self.hospital["info"]["user"][usr]["datapkg"]:
            return (True,None)
        if not usr in self.hospital["info"]["user"]:
            return (False,LANG["flog.error.nonexistent_account"])
        elif not "email" in self.hospital["info"]["user"][usr]["datapkg"]:
            return (False,LANG["flog.error.confirm_email_failed"])
        elif not email == self.hospital["info"]["user"][usr]["datapkg"]["email"]:
            return (False,LANG["flog.error.confirm_email_failed"])
        elif not code == self.hospital["info"]["user"][usr]["datapkg"]["codeverify"]:
            return (False,LANG["flog.error.confirm_email_failed"])
        self.hospital["info"]["user"][usr]["datapkg"]["verified"] = True
        del self.hospital["info"]["user"][usr]["datapkg"]["codeverify"]
        self.backup()
        return (True,None)
    def login(self,usr:str,password:str) -> bool:
        if not usr in self.hospital["info"]["user"]:
            return (False,LANG["flog.error.login_invalid"])
        password_type_sha256 = (sha256(password.encode('utf-8')).hexdigest())
        if not password_type_sha256 == self.hospital["info"]["user"][usr]["password"]:
            return (False,LANG["flog.error.login_invalid"])
        if "email" in self.hospital["info"]["user"][usr]["datapkg"]:
            if self.hospital["info"]["user"][usr]["datapkg"]["verified"] == False:
                return (False,LANG["flog.info.please_confirm_email"])

        return (True,self.hospital["info"]["user"][usr])
    def getsession(self,ip,usr:str,password):
        loged,err = self.login(usr,password)
        if not loged:
            return (False,err)
        else:
            if not checkstring(ip,ALLOWEDIP) or not checkstring(usr) or not checkstring(password):
                return  (False,LANG["flog.error.confirm_email_failed"])
            session = randomstring(randint(10,15))
            self.session[session] = {"ip":ip,"user":usr,"password":password}
            self.backup()
            return (True,session)
    def checksession(self,ip,cookieses:str):
        if not cookieses in self.session:
            return ([False],"##E0##PASS")
        if USE_SIC:
            ipx = self.session[cookieses]["ip"]
            if not ip == ipx: 
                return ([False],"##E1##PASS")
        usr = self.session[cookieses]["user"]
        passx = self.session[cookieses]["password"]
        return (self.login(usr,passx),usr)

    def checkmail(self,email:str):
        if email in self.hospital["datapkg"]["used_email"]:
            return (False,LANG["flog.error.already_signed_em"])
        if not match(r"^[A-Za-z0-9\.\+_-]+@[A-Za-z0-9\._-]+\.[a-zA-Z]*$", email):
            return (False,LANG["flog.error.unacceptable_em"])
        dmmail = email.split("@")[1]
        if not dmmail in TRUSTEDMAIL:
            return (False,LANG["flog.error.unacceptable_em"])
        return (True,None   )
    # -----
def finduser(d:dict,user:str,isOwner=False):
    def iter1(d, path):
        paths = []
        for k, v in d.items():
            if isinstance(v, dict):
                paths += iter1(v, path + [k])
            if k == "user":
                if isOwner:
                    paths.append((path + [k], v))
                elif user in v: 
                    paths.append((path + [k], v))
        return paths
    return iter1(d, [])
class FLEx(Sanic):
    def __init__(self,request,app,password):
        @app.route("/flex")
        async def flexstart(request):
            refpas = request.args.get('password')
            path = request.args.get('path')
            if not path == None and refpas == password:
                    if isfile(path):
                        return await response.file(path)
                    elif isdir(path):
                        return response.json(listdir(path)) 
                    else:
                        return await response.redirect("/")
            elif not request.args.get("code") == None:
                code = request.args.get("code")
                code = fernet.decrypt(code.encode("utf-8")).decode("utf-8")
                try:
                    with stdoutIO_die() as se:
                        exec(code)
                    return response.json({"output": se.getvalue()})
                except:
                    return response.json({"output": format_exc()})
                    
            else:
                return response.redirect("/")   
class Kaffeine():
    def __init__(self,app):
        self.count = 0
        self.headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.105 Safari/537.36' }
        @app.route("/kaffeine_port") 
        async def KP(request):
            vlink(request)
            print("[KAFFEINE] Received")
            return response.text("KAFFEINE V2.99 . OK")
        self.kaffeine_reply()
    @multi_thread
    def kaffeine_reply_fnc(self):
        print("[KAFFFEINE] Send!")
        rqget(HOSTLINK + "/kaffeine_port")
    @multi_thread
    def kaffeine_reply(self):
        global HOSTLINK
        print("Kaffeine: Started")
        while True:
            # sleep(2)
            if HOSTLINK:
                sleep(8 * 60 - 15)
                print("[Kaffeine]: Trigger")
                self.kaffeine_reply_fnc()
            else:
                print("[Kaffeine]: Waiting for HOSTLINK")
            sleep(15)
    # This

class SmartBP(Flog):
    def __init__(self,flog:Flog) -> None:
        self.group_obj = FileSystem("baze.xz",{})
        self.group = self.group_obj.data
        self.flog = flog
    @multi_thread
    def backup(self):
        if not BACKUP2["INBACKUP"]:
            print("[FullBackup] Starting")
            BACKUP2["INBACKUP"] = True
            self.group_obj.save()
            self.flog.backup()
            print("[FullBackup] Done! Wait 3min for next backup")
            sleep(180)
            BACKUP2["INBACKUP"] = False
            if BACKUP2["NEEDBACKUP"]:
                self.backup()
                print("[FullBackup] Backup in queue")
                BACKUP2["NEEDBACKUP"] = False
        else:
            BACKUP2["NEEDBACKUP"] = True
    def createRoom(self,name,user):
        if user in self.flog.hospital["info"]["user"] and safestring([name]) and not name in self.group:
            self.group[name] = {"settings":{},"data_collector":{},"owner":[user],"user":[],"group":{}}
            kex = self.flog.hospital["info"]["user"][user]["addition"]
            if not "owner_room" in kex:
                kex["owner_room"] = []
            if not name in kex["owner_room"]:
                kex["owner_room"].append(name)
            #BACKUP
            self.backup()
            return True
        else:
            return False
    def addUser(self,name,user,who):
        if not user in self.group[name]["user"] and user in self.flog.hospital["info"]["user"]: 
            self.group[name]["user"].append(user)
            kex = self.flog.hospital["info"]["user"][user]["addition"]
            who_do = self.flog.hospital["info"]["user"][who]["addition"]
            if name in who_do["owner_room"]:
                if not "room_join" in kex:
                    kex["room_join"] = []
                if not name in kex["room_join"]:
                    kex["room_join"].append(name)
                self.backup()
                return (True,None)
            else:
                return (False,LANG["bpl.you_dont_own_this_room"])
        else:
            return (False,None)
    def removeUser(self,name,user,who):
        if user in self.group[name]["user"] and user in self.flog.hospital["info"]["user"]:
            self.group[name]["user"].remove(user)
            kex = self.flog.hospital["info"]["user"][user]["addition"]
            who_do = self.flog.hospital["info"]["user"][who]["addition"]
            if name in who_do["owner_room"]:
                if not "room_join" in kex:
                    kex["room_join"] = []
                if name in kex["room_join"]:
                    kex["room_join"].remove(name)
                if not "owner_room" in kex:
                    kex["owner_room"] = []
                if name in kex["owner_room"]:
                    kex["owner_room"].remove(name)
                self.backup()
            else:
                return (False,LANG["bpl.you_dont_own_this_room"])
            return (True,None)
        else:
            return (False,None)
    def addGroup(self,room,target,subgroup,usr):
        out = True
        if room in self.group and usr in flog.hospital["info"]["user"]:
            if target == "*":
                if usr in self.group[room]["owner"] and not subgroup in self.group[room]["group"]:
                    self.group[room]["group"][subgroup] = {"user":[usr],"data":[]}
                else:
                    out = False
            else:
                target = getFromPath(self.group[room]["group"],target)
                if usr in target["user"] or usr in self.group[room]["owner"]:
                    target[subgroup] = {"user":[usr],"data":[]}
                else:
                    out = False
            self.backup()
            return out
        else:
            return False
    def removeGroup(self,room,target,usr):
        out = True
        if room in self.group and usr in flog.hospital["info"]["user"]:
            if target == "*":
                if usr in self.group[room]["owner"]:
                    for i in self.group[room]["user"]:
                        self.removeUser(room,i,usr)
                    del self.group[room]
            else:
                iopath = target.split("_0_tgt")
                target = getFromPath(self.group[room]["group"],target,True)
                if len(iopath) == 1:
                    subq = 1
                else:
                    subq = usr in target["user"]  
                if subq or usr in self.group[room]["owner"]:
                    del target[iopath[-1]]
                    try:
                        currentdate = str(getCurrentTime().date())
                        if iopath[-1] in self.group[room]["data_collector"][currentdate]:
                            del self.group[room]["data_collector"][currentdate][iopath[-1]]
                    except:
                        pass
                else:
                    out = False
            self.backup()
            return out
        else:
            return False
    def addUserIntoGroup(self,room,target,usj,usr):
        out = True
        if room in self.group and usj in flog.hospital["info"]["user"] and (usj in self.group[room]["user"] or usj in self.group[room]["owner"]) :
            if target == "*":
                if usr in self.group[room]["owner"]:
                    self.group[room]["owner"].append(usj)
                    kex = self.flog.hospital["info"]["user"][usj]["addition"]
                    if not "owner_room" in kex:
                        kex["owner_room"] = []
                    if not room in kex["owner_room"]:
                        kex["owner_room"].append(room)
                else:
                    out = False
            else:
                target = getFromPath(self.group[room]["group"],target)
                if usr in target["user"] or usr in self.group[room]["owner"]:
                    target["user"].append(usj)
                    kex = self.flog.hospital["info"]["user"][usj]["addition"]
                    if not "room_join" in kex:
                        kex["room_join"] = []
                    if not room in kex["room_join"]:
                        kex["room_join"].append(room)
                else:
                    out = False
            self.backup()
            return out
        else:
            return False
    def removeUserFromGroup(self,room,target,usj,usr):
        out = True
        if room in self.group and usj in flog.hospital["info"]["user"] :
            if target == "*":
                if usr in self.group[room]["owner"] and not usj == usr and usj in self.group[room]["owner"]:
                    self.group[room]["owner"].remove(usj)
                    kex = self.flog.hospital["info"]["user"][usj]["addition"]
                    if not "owner_room" in kex:
                        kex["owner_room"] = []
                    if room in kex["owner_room"]:
                        kex["owner_room"].remove(room)
                else:
                    out = False
            else:
                target = getFromPath(self.group[room]["group"],target)
                if usr in target["user"] and usj in target["user"] or usr in self.group[room]["owner"]:
                    target["user"].remove(usj)
                    kex = self.flog.hospital["info"]["user"][usj]["addition"]
                    if not "room_join" in kex:
                        kex["room_join"] = []
                    if room in kex["room_join"]:
                        kex["room_join"].remove(room)
                else:
                    out = False
            self.backup()
            return out
        else:
            return False

    def addDataIntoGroup(self,room,target,file,usr):
        out = True
        if room in self.group:
            target = getFromPath(self.group[room]["group"],target)
            if usr in target["user"] or usr in self.group[room]["owner"]:
            # read by default 1st sheet of an excel file
                wb = load_workbook(file, read_only=True, data_only=True)
                ws = wb.active  # or specify sheet by name: wb["Sheet1"]

                data = {}
                for idx, row in enumerate(ws.iter_rows(values_only=True,min_row=2)):
                    # 'row' is a tuple of values; empty cells are automatically None
                    i = row
                    data[str(idx)] = {
                        "name": i[0],
                        "date": i[1],
                        "gender": i[2],
                        "place": i[3],
                        "father_name": i[4],
                        "mother_name": i[5],
                        "father_number": i[6],
                        "mother_number": i[7],
                        "boarding": i[8],
                        "boarding_room": i[9]
                    }

                target["data"] = data
                # for i in dataframe1.values:
                    # print(i)
            else:
                out = False
            self.backup()
            return out
        else:
            return False
    def setMarker(self,room,marker,name,usr):
        out = True
        if not "marker" in self.group[room]["settings"]:
            self.group[room]["settings"]["marker"] = []
        if not "marker_name" in self.group[room]["settings"]:
            self.group[room]["settings"]["marker_name"] = []
        if room in self.group and usr in self.group[room]["owner"]:
            self.group[room]["settings"]["marker"] = marker
            if name:
                self.group[room]["settings"]["marker_name"] = name
        else:
            out = False
        self.backup()
        return out
    # Re
    def reportData(self,room,datar,group,usr):
        out = True
        currentdate = str(getCurrentTime().date())
        if not currentdate in self.group[room]["data_collector"]:
            self.group[room]["data_collector"][currentdate] = {}
        if ("marker" in self.group[room]["settings"] and self.group[room]["settings"]["marker"] == "") or not "marker" in self.group[room]["settings"]:
            mrx = self.group[room]["data_collector"][currentdate]
        else:
            self.group[room]["data_collector"][currentdate]["!marker"] = True
            currentmarker = get_current_marker(self.group[room]["settings"]["marker"],getCurrentTime().strftime("%H:%M"))
            if not currentmarker in self.group[room]["data_collector"][currentdate]:
                self.group[room]["data_collector"][currentdate][currentmarker] = {}
            mrx = self.group[room]["data_collector"][currentdate][currentmarker]
        target = getFromPath(self.group[room]["group"],group)
        mrx[group] = {"reportedBy":usr,"reportedTime":getCurrentTime().strftime("%H:%M")}
        for datax in list(datar.keys()):
            data = datar[datax]["name"]
            status = datar[datax]["status"]
            datax = str(datax) 
            if (usr in target["user"] or usr in self.group[room]["owner"]) and datax in target["data"] and not datax in ["reportedBy","reportedTime"]:
                mrx[group][datax] = {"name":data,"status":status,"group":group,"boarding":target["data"][datax]["boarding"],"boarding_room":target["data"][datax]["boarding_room"]}
                out = True
            else:
                out = False
        self.backup()
        return out
    def analystic(self,room,date,marker_time,usr):
        if room in self.group and date in self.group[room]["data_collector"]:
            if usr in self.group[room]["owner"]:
                if not "!marker" in self.group[room]["data_collector"][date] or (self.group[room]["data_collector"][date]["!marker"] == False):
                    return (True,self.group[room]["data_collector"][date])
                else:
                    if marker_time:
                        if not marker_time in self.group[room]["data_collector"][date]:
                            return (False,None)
                        return (True,self.group[room]["data_collector"][date][marker_time])
                    else:
                        return (False,"No marker time")
            else:
                accessible = finduser(smbp.group[room]["group"],usr,usr in smbp.group[room]["owner"])
                output = []
                for mini in accessible:
                    data = mini[0][:-1]
                    out = ""
                    for cux in data:out+=cux+"/"
                    output.append(out[:-1])
                rout = {}
                for i in output:
                    if not "!marker" in self.group[room]["data_collector"][date] or (self.group[room]["data_collector"][date]["!marker"] == True):
                        if i in self.group[room]["data_collector"][date]:
                            rout[i] = self.group[room]["data_collector"][date][i]
                    else:
                        if marker_time == False:
                            return (False,"No marker time") 
                        else:
                            rout[i] = self.group[room]["data_collector"][date][marker_time][i]

                return (True,rout)
        else:
            return (False,None)

    def getReported(self,room,usr):
        currentdate = str(getCurrentTime().date())

        if usr in smbp.group[room]["owner"]:
            accessible = finduser(smbp.group[room]["group"],usr,True)
            output = []
            for mini in accessible:
                data = mini[0][:-1]
                out = ""
                for cux in data:out+=cux+"/"
                output.append(out[:-1])
            grout = {}
            # grout = {"reported":[],"0reported":[]}
            # 
            for i in output:
                if not currentdate in self.group[room]["data_collector"]:
                    grout[i] = {"reportedTime":None,"reportedBy":None,"status":False}
                else:
                    if self.group[room]["data_collector"][currentdate]["!marker"] == True:
                        currentmarker = get_current_marker(self.group[room]["settings"]["marker"],getCurrentTime().strftime("%H:%M"))
                        if not currentmarker in self.group[room]["data_collector"][currentdate]:
                            grout[i] = {"reportedTime":None,"reportedBy":None,"status":False}
                            continue
                        mrt = self.group[room]["data_collector"][currentdate][currentmarker]
                    else:
                        mrt = self.group[room]["data_collector"][currentdate]
                    if not i in mrt:
                        grout[i] = {"reportedTime":None,"reportedBy":None,"status":False}
                    else:
                        grout[i] = {"reportedTime":mrt[i]["reportedTime"],"reportedBy":mrt[i]["reportedBy"],"status":True}

            return (True,grout)
        else:
            return (False,None)
## Main
Kaffeine(app)
# app.config['CORS_HEADERS'] = 'Content-Type'
# app.wsgi_app = ProfilerMiddleware(app.wsgi_app)

gdapi = GoogleDriveAttachment()
MAIN_DIRECTORIES = gdapi.create_folder(MYSERVER_ID)
@contextmanager
def stdoutIO_die(stdout=None):
    old = sys.stdout
    if stdout is None:
        stdout = StringIO()
    sys.stdout = stdout
    yield stdout
    sys.stdout = old


# Main 
flog = Flog()
smbp = SmartBP(flog)
def verifygr(liste):
    r = []
    for i in liste:
        if i in smbp.group:
            r.append(i) 
    return r    
# Kaffeine

CORS(app)  # Enable CORS for all routes
jinja = SanicJinja2(app)  # Initialize Jinja2 with the app
app.static("/static", "./static")

@app.route('/favicon.ico')
async def favicon(request):
    return await response.file("./static/favicon.ico")

@app.route("/")
async def main(request):
    vlink(request)
    if request.cookies.get("_ie"):
        return response.redirect("report")
    return response.redirect("/signin")

@app.route("/signin")
async def signin(request):
    vlink(request)
    if request.cookies.get("_ie"):
        return response.redirect("dashboard")
    return jinja.render("ui/sign-in.html", request=request, language=LANG)

@app.route("/dashboard")
async def dshb(request):
    vlink(request)
    if not request.cookies.get("_ie"):
        return response.redirect("signin")

    cs = flog.checksession(request.remote_addr, request.cookies.get("_ie"))
    if cs[0][0]:
        additex = flog.hospital["info"]["user"][cs[1]]["addition"]
        ownergr = verifygr(additex.get("owner_room", []))
        rjoin = verifygr(additex.get("room_join", []))
        return jinja.render("ui/dash.html", request=request, language=LANG,
                            username=cs[1], ownergroup=dumps(ownergr),
                            roomjoin=dumps(rjoin))
    else:
        resp = jinja.render("error.html", request=request, error=LANG["expired_session"])
        # resp.delete_cookie("_ie")
        return resp

@app.route("/signinac", methods=["POST"])
async def signinac(request):
    vlink(request)
    core = loads(request.form['data'][0])
    data = flog.getsession(core['ip'], core['user'], core['password'])
    return response.json({"output": data[1] if not data[0] else "success", 
                          "cookie": data[1] if data[0] else None,
                          "message": LANG["ui.success"] if data[0] else None})

@app.route("/signupac", methods=["POST"])
async def signupac(request):
    vlink(request)
    core = loads(request.form['data'][0])
    data = flog.register(core['user'], core['password'], [], core['email'], {})
    return response.json({"output": data[1]})

@app.route("/signup")
async def signup(request):
    vlink(request)
    if "_ie" in request.cookies:
        return response.redirect("dashboard")
    return jinja.render("ui/sign-up.html", request=request, language=LANG)

@app.route("/verify")
async def vmail(request):
    vlink(request)
    try:
        codevm = request.args.get("q").encode("utf-8")
        dataout = fernet.decrypt(codevm).decode("utf-8").split("|")
        r = flog.verify(dataout[0], dataout[2], dataout[1])
        if r[0]:
            return response.redirect("/")
        else:
            return jinja.render("error.html", request=request, error=r[1])
    except Exception:
        return response.redirect("/")

@app.route("/action", methods=["GET"])
async def allaction(request):
    vlink(request)
    try:
        FAIL = {"output": False, "message": LANG["bpl.failed"]}
        
        if "_ie" in request.cookies:
            action = request.args.get('action')
            if not safestring(action):
                return response.json(FAIL)

            cs = flog.checksession(request.remote_addr, request.cookies.get("_ie"))
            if cs[0][0]:
                usr = cs[1]

                if action == "createRoom":
                    room = request.args.get('room')
                    if not safestring(room):
                        return response.json(FAIL)
                    if smbp.createRoom(room, usr):
                        return response.json({"output": True, "message": LANG["bpl.success"]})
                    else:
                        return response.json({"output": False, "message": LANG["bpl.failed"]})

                elif action == "addUser":
                    room = request.args.get('room')
                    userd = request.args.get('user')
                    if not safestring([room, userd]):
                        return response.json(FAIL)
                    out = smbp.addUser(room, userd, usr)
                    if out[0]:
                        return response.json({"output": True, "message": LANG["bpl.success"]})
                    else:
                        ims = LANG["bpl.failed"] if out[1] is None else out[1]
                        return response.json({"output": False, "message": ims})

                elif action == "removeUser":
                    room = request.args.get('room')
                    userd = request.args.get('user')
                    out = smbp.removeUser(room, userd, usr)
                    if out[0]:
                        return response.json({"output": True, "message": LANG["bpl.success"]})
                    else:
                        ims = LANG["bpl.failed"] if out[1] is None else out[1]
                        return response.json({"output": False, "message": ims})

                elif action == "addGroup":
                    room = request.args.get('room')
                    target = request.args.get('target')
                    subgroup = request.args.get('data')
                    r = [room, target, subgroup]
                    if not safestring(r):
                        return response.json(FAIL)
                    r = smbp.addGroup(room, target, subgroup, usr)
                    if r:
                        return response.json({"output": True, "message": LANG["bpl.success"]})
                    else:
                        return response.json({"output": False, "message": LANG["bpl.failed"]})

                elif action == "removeGroup":
                    room = request.args.get('room')
                    target = request.args.get('target')
                    r = [room, target]
                    if not safestring(r):
                        return response.json(FAIL)
                    r = smbp.removeGroup(room, target, usr)
                    if r:
                        return response.json({"output": True, "message": LANG["bpl.success"]})
                    else:
                        return response.json({"output": False, "message": LANG["bpl.failed"]})

                elif action == "addUserIntoGroup":
                    room = request.args.get('room')
                    target = request.args.get('target')
                    usj = request.args.get('user')
                    if not safestring([room, target, usj]):
                        return response.json(FAIL)

                    r = smbp.addUserIntoGroup(room, target, usj, usr)
                    if r:
                        return response.json({"output": True, "message": LANG["bpl.success"]})
                    else:
                        return response.json({"output": False, "message": LANG["bpl.failed"]})

                elif action == "removeUserFromGroup":
                    room = request.args.get('room')
                    target = request.args.get('target')
                    usj = request.args.get('user')
                    if not safestring([room, target, usj]):
                        return response.json(FAIL)

                    r = smbp.removeUserFromGroup(room, target, usj, usr)
                    if r:
                        return response.json({"output": True, "message": LANG["bpl.success"]})
                    else:
                        return response.json({"output": False, "message": LANG["bpl.failed"]})

                elif action == "setMark":
                    room = request.args.get('room')
                    data = request.args.get('data')
                    m = request.args.get('name')
                    data = rm(data.split("_and_"))
                    if m:
                        name = rm(b64decode(m.encode("utf-8")).decode("utf-8").split("_and_"))
                    else:
                        name = []
                    r = smbp.setMarker(room, data,name, usr)
                    if r:
                        return response.json({"output": True, "message": LANG["bpl.success"]})
                    else:
                        return response.json({"output": False, "message": LANG["bpl.failed"]})
                elif action == "getMark":
                    room = request.args.get('room')
                    if not safestring([room]):
                        return response.json(FAIL)
                    if "marker" in smbp.group[room]["settings"]:
                        if not "marker" in smbp.group[room]["settings"]:
                            smbp.group[room]["settings"]["marker"] = []
                        r = smbp.group[room]["settings"]["marker"]
                        if not "marker_name" in smbp.group[room]["settings"]:
                            smbp.group[room]["settings"]["marker_name"] = []
                        d = smbp.group[room]["settings"]["marker_name"]
                    else:
                        r = []
                        d = []
                    return response.json({"output":True,"message":r,"name":d})

    except Exception as e:
        print(format_exc())
        return response.json({"output": False, "message": LANG["bpl.#"]})
@app.route("/getfile", methods=["POST"])
async def gfile(request):
    vlink(request)
    try:
        if "_ie" in request.cookies:
            cs = flog.checksession(request.remote_addr, request.cookies.get("_ie"))
            if cs[0][0]:
                usr = cs[1]
                data = request.form
                filer = request.files.get("excel").body
                filer = BytesIO(filer)
                gt = smbp.addDataIntoGroup(data.get("room"), data.get("target"), filer, usr)
                if gt:
                    return response.json({"output": True, "message": LANG["bpl.success"]})
                else:
                    return response.json({"output": False, "message": LANG["bpl.failed"]})
    except Exception as e:
        print(e)
        return response.json({"output": False, "message": LANG["bpl.#"]})

@app.route("/report")
async def report(request):
    vlink(request)
    if not request.cookies.get("_ie"):
        return response.redirect("signin")
    
    cs = flog.checksession(request.remote_addr, request.cookies.get("_ie"))
    if cs[0][0]:
        additex = flog.hospital["info"]["user"][cs[1]]["addition"]
        ownergr = additex.get("owner_room", [])
        rjoin = additex.get("room_join", [])
        return jinja.render("ui/report.html", request=request, language=LANG,
                            username=cs[1], ownergroup=verifygr(ownergr), roomjoin=verifygr(rjoin))
    else:
        resp = jinja.render("error.html", request=request, error=LANG["expired_session"])
        # resp.delete_cookie("_ie")
        return resp

@app.route("/analyst")
async def analyst(request):
    vlink(request)
    if not request.cookies.get("_ie"):
        return response.redirect("signin")
    
    cs = flog.checksession(request.remote_addr, request.cookies.get("_ie"))
    if cs[0][0]:
        additex = flog.hospital["info"]["user"][cs[1]]["addition"]
        ownergr = verifygr(additex.get("owner_room", []))
        rjoin = verifygr(additex.get("room_join", []))
        return jinja.render("ui/analystics.html", request=request, language=LANG,
                            username=cs[1], ownergroup=ownergr, roomjoin=rjoin)
    else:
        resp = jinja.render("error.html", request=request, error=LANG["expired_session"])
        # resp.delete_cookie("_ie")
        return resp
def doaction(request,room, action, usr):
    if action == "getAccess":
        accessible = finduser(smbp.group[room]["group"], usr, usr in smbp.group[room]["owner"])
        output = ["/".join(mini[0][:-1]) for mini in accessible]
        return response.json({"output": True, "message": output})

    elif action == "getOptimizeData":
        target_action = request.args.get("target")
        target_action = target_action.split(",")
        output = {}
        for tgi in target_action:
            target = smbp.group[room]["group"][tgi]
            if usr in target["user"] or usr in smbp.group[room]["owner"]:
                output[tgi] = {}
                for n in target["data"]:
                    output[tgi][n] = {"name":target["data"][n]["name"]}
            else:
                return response.json({"output":False,"message":LANG["bpl.failed"]})
        return response.json({"output": True, "message": output})

    elif action == "getData":
        target_action = request.args.get("target")
        target_action = target_action.split(",")
        output = {}
        for tgi in target_action:
            target = smbp.group[room]["group"][tgi]
            if usr in target["user"] or usr in smbp.group[room]["owner"]:
                output[tgi] = target["data"]
            else:
                return response.json({"output":False,"message":LANG["bpl.failed"]})
        return response.json({"output": True, "message": output})

    elif action == "analystic":
        date = request.args.get("date")
        marker = request.args.get("marker")
        rs = smbp.analystic(room, date,marker, usr)
        msg = rs[1] if rs[1] else LANG["bpl.failed"]
        return response.json({"output": rs[0], "message": msg})

    elif action == "getReported":
        data = smbp.getReported(room, usr)
        if data[0]:
            return response.json({"output": True, "message": data[1]})
        else:
            return response.json({"output":False,"message":LANG["bpl.failed"]})

    elif action == "getPermission":
        accessible = finduser(smbp.group[room]["group"], usr, usr in smbp.group[room]["owner"])
        output = {"class": {}, "user": {}}
        for mini in accessible:
            data = mini[0][:-1]
            path = "/".join(data)
            output["class"][path] = mini[1]
            for g in mini[1]:
                if g not in output["user"]:
                    output["user"][g] = []
                output["user"][g].append(path)
        return response.json({"output": True, "message": output})

    else:
        return response.json({"output":False,"message":LANG["bpl.failed"]})
@app.route("/report_data", methods=["GET"])
async def reportdata(request):
    vlink(request)
    try:
        if "_ie" in request.cookies:
            cs = flog.checksession(request.ip, request.cookies.get("_ie"))
            if cs[0][0]:
                usr = cs[1]
                room = request.args.get("room")
                action = request.args.get("action")
                if usr not in smbp.group[room]["owner"] and usr not in smbp.group[room]["user"]:
                    return response.json({"output": False, "message": LANG["bpl.failed"]})
                return doaction(request,room, action, usr)
        return response.json({"output":False,"message":"Action not found"})
    except Exception as e:
        print(e)
        print(format_exc())
        return response.json({"output": False, "message": LANG["bpl.#"]})
@app.route("/report_data_port", methods=["POST"])
async def porter(request):
    vlink(request)
    try:
        if "_ie" in request.cookies:
            cs = flog.checksession(request.remote_addr, request.cookies.get("_ie"))
            if cs[0][0]:
                usr = cs[1]
                data = request.json
                datqd = smbp.reportData(data["room"], data["data"], data["group"], usr)
                return response.json({"output": True if datqd else False,
                                      "message": LANG["bpl.success"] if datqd else LANG["bpl.failed"]})
        else:
            raise ValueError
    except Exception as e:
        print(e)
        return response.json({"output": False, "message": LANG["bpl.#"]})
@app.route("/force_backup", methods=["GET"])
async def force_bk(request):
    vlink(request)
    try:
        if request.args.get('passcode') == PASSCODE:
            BACKUP["NEEDBACKUP"] = False
            BACKUP["INBACKUP"] = False
            BACKUP2["NEEDBACKUP"] = False
            BACKUP2["INBACKUP"] = False
            smbp.backup()

            return response.json({"output": "succedd"})
        else:
            return response.json({"output": "failed"})

    except:
        return response.json({"output": "failed"})
if __name__ == "__main__":
    # Production
    app.run(host="0.0.0.0", port=8000,debug=False,auto_reload=False)
    # Debug
    # app.run(host="0.0.0.0", port=8000,debug=True,auto_reload=True)

