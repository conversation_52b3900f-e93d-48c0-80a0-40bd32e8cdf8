<!DOCTYPE html>
<html lang="en">
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BPlan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"/>
    <script src="
    https://cdn.jsdelivr.net/npm/sweetalert2@11.12.4/dist/sweetalert2.all.min.js
    "></script>
    <link href="
    https://cdn.jsdelivr.net/npm/sweetalert2@11.12.4/dist/sweetalert2.min.css
    " rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/static/img/favicon.png">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/all.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-duotone-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-thin.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-regular.css">
    <link rel="stylesheet" href="/static/nrc/report_style.css">
</head>
<body>
    <div class="background"></div>
    <div class="container" id="ijx1">
        <div style="height: fit-content;overflow: auto;" id="ijx2">
            <h3 class="appear intro" id="title"></h3>
            <div class="mainform" >
                <div class="suform" id="suf1">
                    <select class="form-select" name="room_choose" id="room_choose">
                    </select>
                </div>

                <div class="suform" id="suf2">
                    
                    <select class="form-select" name="group_choose" id="group_choose">
                        
                    </select>

                </div>
                <div class="suform" style="width: 100%;flex: 0 0 25%;white-space: nowrap;">
                    <button type="button" class="btn btn-dark" id="submit" style="white-space: nowrap;">{{language["bpl.submit"]}}</button>
                    <button type="button" class="btn btn-dark" onclick="window.location.href = window.location.origin + `{{url_for('dshb')}}`" style="white-space: nowrap;">
                        <i class="fa-solid fa-left"></i>
                     </button>
                </div>
            </div>

            <hr>
            <div id="header_tb" style="display: flex;">
                <ul class="flexible" id="header"><h3>Họ tên</h3><h3>D</h3><h3>P</h3><h3>K</h3></ul>
            </div>
        </div>
        <div class="intro " style="overflow: auto" id="ijx3">
            <div class="flexible" id="student_choose" style="overflow-y:auto;">
                </ul>
            </div>

        </div>
        <small style="color: white;">(C) 2024 By HoangDat</small>

    </div>
</body>
<script>
    function setCookie(name,value,days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days*24*60*60*1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "")  + expires + "; path=/";
    }
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i=0;i < ca.length;i++) {
            var c = ca[i];
            while (c.charAt(0)==' ') c = c.substring(1,c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
        }
        return null;
    }
    if (getCookie("rcaching")){
        window.rcaching = JSON.parse(getCookie("rcaching"));
    }else{
        window.rcaching = {};

    }
    function setCookie(name,value,days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days*24*60*60*1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "")  + expires + "; path=/";
    }
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i=0;i < ca.length;i++) {
            var c = ca[i];
            while (c.charAt(0)==' ') c = c.substring(1,c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
        }
        return null;
    }
    $("#ijx3").css("height", ($("#ijx1").height() - $("#ijx2").height() - 20) + "px" ) 
    // 
    var offlinewr = "{{language['bpl.wr_T']}}";
    var offlinewtr = "{{language['bpl.wtr_T']}}";
    function $g(id) {
        return document.getElementById(id)
    };

    

    ownergr = {{ownergroup | safe}};roomjoin = {{roomjoin | safe}};
    ownergr.forEach(element => {
        data = document.createElement("option");
        data.value = element;
        data.innerHTML = element;
        $g("room_choose").appendChild(data)
    });
    roomjoin.forEach(element => {
        data = document.createElement("option");
        data.value = element;
        data.innerHTML = element;
        $g("room_choose").appendChild(data)
    });

    function addReport(strf,loop=0){
        var node_1 = document.createElement('UL');
        node_1.setAttribute('class', 'flexible');

        var node_2 = document.createElement('h6');
        node_1.appendChild(node_2);

        var node_3 = document.createTextNode(loop + 1 + ". " + (new String(strf)));
        node_2.appendChild(node_3);

        var node_4 = document.createElement('LI');
        node_1.appendChild(node_4);

        var node_5 = document.createElement('INPUT');
        node_5.setAttribute('type', 'radio');
        node_5.setAttribute('class', 'form-check-input');
        node_5.setAttribute('checked', 'true');
        node_5.setAttribute('value', 'online');
        node_5.setAttribute('name', loop);
        node_4.appendChild(node_5);

        var node_6 = document.createElement('LI');
        node_1.appendChild(node_6);

        var node_7 = document.createElement('INPUT');
        node_7.setAttribute('type', 'radio');
        node_7.setAttribute('class', 'form-check-input');
        node_7.setAttribute('value', 'offline_wr');
        node_7.setAttribute('name', loop);
        node_6.appendChild(node_7);
        
        var node_8 = document.createElement('LI');
        node_1.appendChild(node_8);
        var node_9 = document.createElement('INPUT');
        node_9.setAttribute('type', 'radio');
        node_9.setAttribute('class', 'form-check-input');
        node_9.setAttribute('value', 'offline_wtr');
        node_9.setAttribute('name', loop);
        node_8.appendChild(node_9);


        document.getElementById("student_choose").appendChild(node_1)
        // $g("student_choose").appendChild(document.createElement("hr"))

    }
    async function getSub(url,caching=false,showloading=false){
        rnotify = null;
        if (showloading){
                    rnotify = Swal.fire({
                title: "{{language['wp.waiting']}}",
                html: "{{language['wp.request_inprocess']}}",
                didOpen: () => {
                    Swal.showLoading();
                },
                willClose: () => {

                },
                allowOutsideClick :false
            })
        }
        if (caching == true){
            if (url in window.rcaching && window.rcaching[url] != null){
                if (rnotify){rnotify.close()}
                return window.rcaching[url];
            }
        }
        var data;
        await fetch(url)
            .then((response) => {
                return response.json()
            })
            .then((datar) => {
                data = datar;
            })
        window.rcaching[url] = data;
        setCookie("rcaching",JSON.stringify(window.rcaching),7)
        if (rnotify){rnotify.close()}
        return data;
    }
    async function core3d() {
        data = await getSub("/report_data?room=" + $g("room_choose").value + "&action=getOptimizeData&target="+$g("group_choose").value,true,true)
        $g("student_choose").innerHTML = "";
        if (data["output"]==true){
            out = 0
            for ( ik in data["message"][$g("group_choose").value]) {
                element = data["message"][$g("group_choose").value][ik]["name"];
                addReport(element,out);
                out += 1;
                        
            }
        }
    }
    async function coreddd() {
        data = "{{ language['bpl.report'] }}" + " : " + $g("room_choose").value;
        $g("title").innerHTML = data;
        data = await getSub("/report_data?room=" + $g("room_choose").value + "&action=getAccess",true)
        $g("group_choose").innerHTML = "";
        if (data["output"]==true){
            gt = data["message"];
            for ( ik in gt) {
                element = gt[ik];
                data = document.createElement("option");
                data.value = element;
                data.innerHTML = element;
                $g("group_choose").appendChild(data);
                
            }
        }
        core3d();
    }
    function submit(){
        out = {};
        grouter = {};
        for (let i = 0; i < $g("student_choose").children.length; i++) {
            const element = $g("student_choose").children[i];
            name = element.getElementsByTagName("h6")[0].innerHTML;
            name = name.substring(name.indexOf(".") + 1);
            dta = ($('input[name="'+i+'"]:checked').val());
            out[i] = {"name": name,"status":dta};
            if (dta.includes("offline")){
                grouter[i] = {"name": name,"status":dta};
            }
            
        };
        data = JSON.stringify({
            "room":$g("room_choose").value,
            "group":$g("group_choose").value,
            "data":out
        });
        console.log(data)
        fetch("/report_data_port", {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json;charset=utf-8'
                },
                body: data
            })
            .then(function (res) {
                return res.json();
            })
            .then(function (data) {
                console.log(data)
                if (data["output"] == false){
                    title = "Error!";
                    icon = "error";
                    mesg = data["message"];
                    Swal.fire({
                        icon: icon,
                        title: title,
                        text: mesg,
                    });
                }else{
                    title = "Success!";
                    icon = "success";
                    mesg = "<p>"
                    for (const keydie in grouter) {
                        const keytie = grouter[keydie];
                        if (keytie["status"] == "offline_wr"){
                            statusior = offlinewr;
                        }else{
                            statusior = offlinewtr;
                        }
                        mesg += keytie["name"]+ " : " + statusior + "<br/>";
                    };
                    mesg += "</p>"
                    Swal.fire({
                        icon: icon,
                        title: title,
                        html: mesg,
                    });
                }

            })

    }

    coreddd();
    $g("room_choose").onchange = coreddd;
    $g("group_choose").onchange = core3d;
    $g("submit").onclick = submit;

    if (!(roomjoin.length + ownergr.length > 1)){
        $g("suf1").style.display = "none";
        $g("suf2").style.flex = "0 0 65%";

    }else{
        $g("suf1").style.flex = "0 0 35";
        $g("suf2").style.flex = "0 0 30%";
    }
</script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="/static/js/uloaderjs.js"></script>

</html>