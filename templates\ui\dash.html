<!DOCTYPE html>
<html lang="en">
<script src="https://code.jquery.com/jquery-3.7.1.min.js"
    integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BPlan</title>
    <link rel="stylesheet" data-purpose="Layout StyleSheet" title="Web Awesome" href="/css/app-wa-09b459cf485d4b1f3304947240314c05.css?vsn=d">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/all.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-duotone-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-thin.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-regular.css">

    <link rel="icon" type="image/x-icon" href="/static/img/favicon.png">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-light.css">  <!-- https://fontawesome.com/-->  
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    </script>
    <script src="
    https://cdn.jsdelivr.net/npm/sweetalert2@11.12.4/dist/sweetalert2.all.min.js
    "></script>
    <link href="
    https://cdn.jsdelivr.net/npm/sweetalert2@11.12.4/dist/sweetalert2.min.css
    " rel="stylesheet">
    <link rel="stylesheet" href="/static/css/reb.css">
    <link rel="stylesheet" href="/static/nrc/dash_style.css">
</head>

<body>
    <div class="background"></div>
    <div class="container"  >
        <div class="container-fluid h-100">
            <div class="row justify-content-center h-100">
                <div class="col-md-4 col-xl-3 intro" id="maingroup" >
                    <div class="card mb-sm-3 mb-md-0 contacts_card" style="height: 100%;top: 0;"  >
                        <div class="card-header">
                            <div class="input-group">
                                <input type="text" placeholder="{{language['bpl.createRoom']}}" name="" id="createRoom" class="form-control search">
                                <div class="input-group-prepend" id="createRoom_btn">
                                    <span style="height: 100%;" class="input-group-text search_btn"><i class="fa-duotone fa-solid fa-plus"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body contacts_body" >
                            <h6 style="opacity: 0.45;position: absolute;width: 70%;margin-left: auto;margin-right: auto;left: 0;right: 0;bottom: 0;text-align: center;text-wrap: balance;white-space: pre-line;word-break: break-word;color: white;">Cảm ơn thầy Mai Đình Vinh - Hiệu trưởng trường PTDTBT THCS Khun Há đã tài trợ cho dự án</h6>                        <h3 style="
                            opacity: 0.45;
                            position: relative;
                            text-align: center;

                            top: 45%;
                            color: white;
                        " id="showupe">{{language["bpl.0group_att"]}}</h3>
                            <ui class="contacts" id="contacts">
                            </ui>
                        </div>
                        <div class="card-footer"></div>
                    </div>
                </div>
                <div class="col-md-8 col-xl-6 intro2" id="maincons"  style="opacity: 0;">
                    <div class="card">
                        <div class="card-header msg_head">
                            <div class="d-flex bd-highlight">
                                <div class="img_cont">
                                    <div class="img_cont rounded-circle" style="background-color: gray;"><div class="d-flex align-items-center justify-content-center rounded-circle user_img"><i class="fa-duotone fa-solid fa-user" style="font-size: xx-large;"></i></div><span class="online_icon"></span></div>
                                    <span class="online_icon"></span>
                                </div>
                                <div class="user_info">
                                    <span id="currentGroup">BPlan</span>
                                </div>
                                <div class="video_cam">
                                    <span id="vdx_signout"><i class="fa-duotone fa-solid fa-right-from-bracket"></i></span>
                                </div>
                            </div>
                            <span id="action_menu_btn"><i class="fas fa-ellipsis-v"></i></span>
                            <div class="action_menu">
                                <ul>
                                    <li id="addUser_btn"><i class="fas fa-user-circle"></i> {{language["bpl.addUser"]}}</li>
                                    <li id="removeUser_btn"><i class="fas fa-users"></i> {{language["bpl.removeUser"]}}</li>
                                    <li id="addGroup_btn"><i class="fas fa-plus"></i> {{language["bpl.addGroup"]}}</li>
                                    <li id="removeGroup_btn"><i class="fas fa-ban"></i> {{language["bpl.removeGroup"]}}</li>
                                    <li id="addUserIntoGroup_btn"><i class="fa-duotone fa-solid fa-user-plus"></i> {{language["bpl.addUserIntoGroup"]}}</li>
                                    <li id="removeUserFromGroup_btn"><i class="fa-duotone fa-solid fa-user-minus"></i> {{language["bpl.removeUserFromGroup"]}}</li>
                                    <li id="showdatabase_report" onclick="viewex_Core()"><i class="fa-sharp-duotone fa-solid fa-eye"></i><content id="customcont">{{language["bpl.show_database"]}}</content></li>
                                    <li id="editMarker"><i class="fa-solid fa-calendar-xmark"></i> {{language["bpl.editMarker"]}}</li>
                                    <li><a style="color: white;text-decoration: none;" href="/static/assets/blank.xlsx"><i class="fa-solid fa-download"></i> {{language["bpl.download_template"]}}</a></li>
                                    <li  style="width: 100%;">
                                        <label style="
                                        width: 100%;
                                    ">
                                            <input type="file" id="fileExcel" style="display: none;">
                                            <i class="fas fa-paperclip"></i>
                                            {{language["bpl.import_data"]}}
                                        </label>
                                    </li>
                                    <li id="username"><i class="fa-duotone fa-solid fa-user"></i> {{language["bpl.usrname"]}} {{username}}</li>
                                
                                </ul>
                            </div>
                        </div>
                        <div class="card-body msg_card_body" >
                            <div id="mcontainer" style="display: none;">
                            </div>
                            <div id="lcontainer" style="display: block;">
                                <table border="1" class="rwd-table" id="studentboard" style="
                                flex: 0 0 100%;
                                height: 100%;
                                width: 100%;
                            ">
                
                                        <tr>
                                            <th>{{language["bpl.reportedBy"]}}</th>
                                            <th>{{language["bpl.name"]}}</th>
                                            <th>{{language["bpl.reportedTime"]}}</th>
                                            <th>{{language["bpl.reported"]}}</th>
                                            <th>{{language["bpl.0reported"]}}</th>
                                          </tr>
                
                                      </table>
                            </div>
                            <div id="ncontainer" style="display: none;">
                                
                                <table border="1" class="rwd-table" id="permission_table" style="
                                flex: 0 0 100%;
                                height: 100%;
                                width: 100%;
                                ">
                                    <thead id="permissionboard_class">
                                        
                                    </thead>
                                    <tbody id="permission_board">

                                    </tbody>                                    
                                </table>
                                </div>
                            </div>
                        <div class="card-footer">
                            <div class="input-group">
                                <div class="input-group-append ncp"  onclick="window.location.href = window.location.origin + '/analyst';">
                                    <span class="input-group-text d-flex justify-content-center attach_btn"><i class="fa-duotone fa-solid fa-chart-simple" style="padding-right: 3px;"></i> {{language["bpl.analystic"]}}</span>
                                </div>

                                <div class="input-group-append ncp" onclick="window.location.href = window.location.origin + '/report';">
                                    <span class="input-group-text send_btn d-flex justify-content-center"><i class="fas fa-location-arrow" style="padding-right: 3px;"></i> {{language["bpl.report"]}}</span>
                                </div>


                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/smooth-scrollbar/8.8.4/smooth-scrollbar.min.js" integrity="sha512-UOuvdHxPTS8D5IoOYOwLGAN05jYYXKhxFOZDe/24o53eOOf9ylws0uPfV+gRj/k1z17C0KtC7Vkt+5H7BLQxOA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/smooth-scrollbar/8.8.4/plugins/overscroll.min.js" integrity="sha512-r0mvJ5yIFzuQ9ExHaNeSkBCo4G5XtXs4yyyYozSR3suiFfNhqgoc14P0tVKkqovImxcCcLAkWlQ9B5hUl0gfdA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/js-base64@3.7.8/base64.min.js"></script>
    <script>
window.currentview = 0;
window.currentSubGroup = "*";
$(document).ready(function () {
    $('#action_menu_btn').click(function () {
        $('.action_menu').toggle();
    });
});
    // function
    $g("vdx_signout").onclick = function(){
        document.cookie = "_ie" +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie = "rcaching" +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        setTimeout(function(){window.location.reload()},500);
    };
    var Scrollbar = window.Scrollbar;
    Scrollbar.use(window.OverscrollPlugin);
    Scrollbar.initAll( {"damping":0.1,    plugins: {
        overscroll:  true,
        },"continuousScrolling":true,"renderByPixels":true});

function $g(id){
    return document.getElementById(id);
};
function viewex_Core(){
    if (window.currentview == 0) {
        $g("mcontainer").style.display = "none";
        $g("lcontainer").style.display = "block";
        $g("ncontainer").style.display = "none";
        $g("customcont").innerHTML = "{{language['bpl.show_database']}}";
        window.currentview = 1;
    }else if (window.currentview == 1){
        reportData_gac();
        $g("mcontainer").style.display = "block";
        $g("lcontainer").style.display = "none";
        $g("ncontainer").style.display = "none";
        window.currentview = 2;            
        $g("customcont").innerHTML = "{{language['bpl.show_permission']}}";
    }else{
        dia_perm();
        $g("mcontainer").style.display = "none";
        $g("lcontainer").style.display = "none";
        $g("ncontainer").style.display = "block";
        $g("customcont").innerHTML = "{{language['bpl.show_reported']}}";
        window.currentview = 0;            
    }
}
viewex_Core();
var fit_lsp,fith_lsp,fit_lap;
var lsp = $('#maingroup');
var lap = $("#maincons");
lsp.css("width","100%");
lsp.css("height","100%");       


function bind(){
    $('.groupclicker').on('click',function(){
        if (!(window.triggerGrClick)){
            lsp.css("display","none");
            lap.addClass("showsup"); 
            lsp.addClass("chat") ;
            lap.addClass("chat") ;
            lap.css("width","100%");
            lap.css("height","100%");       
        
            $('.active').removeClass('active');
            this.classList = ["active groupclicker"];
            window.room = this.getElementsByTagName("span")[1].innerHTML;
            $g("currentGroup").innerHTML = this.getElementsByTagName("span")[1].innerHTML;
            reported_gac();
            window.triggerGrClick = true;
        }


    
})
}
origin = $g("studentboard").innerHTML;
dorigin = $g("permission_board").innerHTML;
b  = $g("studentboard").innerHTML;
db  = $g("permission_board").innerHTML;
// msx
function addGroup_style(user,info){
    var node_1 = document.createElement('LI');
    // node_1.setAttribute('class', 'active');
    node_1.setAttribute('class', 'groupclicker');

    var node_2 = document.createElement('DIV');
    node_2.setAttribute('class', 'd-flex bd-highlight');
    node_1.appendChild(node_2);

    var node_3 = document.createElement('DIV');
    node_3.setAttribute('class', 'img_cont rounded-circle');
    node_3.setAttribute('style', 'background-color: gray;');
    node_2.appendChild(node_3);

    var node_4 = document.createElement('DIV');
    node_4.setAttribute('class', 'd-flex align-items-center justify-content-center rounded-circle user_img');
    node_3.appendChild(node_4);

    var node_5 = document.createElement('I');
    node_5.setAttribute('class', 'fa-duotone fa-solid fa-user');
    node_5.setAttribute('style', 'font-size: xx-large;');
    node_4.appendChild(node_5);

    var node_6 = document.createElement('SPAN');
    node_6.setAttribute('class', 'online_icon');
    node_3.appendChild(node_6);

    var node_7 = document.createElement('DIV');
    node_7.setAttribute('class', 'user_info ncp');
    node_2.appendChild(node_7);

    var node_8 = document.createElement('SPAN');
    node_7.appendChild(node_8);

    var node_9 = document.createTextNode((new String(user)));
    node_8.appendChild(node_9);

    var node_10 = document.createElement('p');
    node_10.innerHTML = info;
    node_7.appendChild(node_10);
    document.getElementById("contacts").appendChild(node_1);
    bind();
}
function strfm(string,list){
    for (let I = 0; I < list.length; I++) {
        string = string.replace("{"+I+"}",list[I]);
    }
    return string;
}
function getElementByXpath(path) {
    return document.evaluate(path, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
}
function reped(list){
    var node_1 = document.createElement('TR');
    for (let index = 0; index < list.length; index++) {
        const element = list[index];
        var node_2 = document.createElement('TD');
        node_2.innerHTML = element;
        node_1.appendChild(node_2);
            
    }
    $g("studentboard").appendChild(node_1)
}
function reged(id,list,elem="TD"){
    var node_1 = document.createElement('TR');
    for (let index = 0; index < list.length; index++) {
        const element = list[index];
        var node_2 = document.createElement(elem);
        node_2.innerHTML = element;
        node_1.appendChild(node_2);
            
    }
    $g(id).appendChild(node_1);
}
function reported_gac(){
    fetch("/report_data?room=" + window.room + "&action=getReported")
        .then((response) => {
            return response.json();
        })
        .then((data) => {
            $g("studentboard").innerHTML = b ;
            if(data["output"]==true){
                data = data["message"];
                for (const de in data) {
                    if (data[de]["status"]) {
                        re = ['x',''];
                    }else{
                        re = ['','x'];

                    }
                    reped([data[de]["reportedBy"],
                        de,
                        data[de]["reportedTime"],
                        re[0],
                        re[1]
                    ]
                );
                }
            // gru[]
            // addReported_gac(element);
            }
        });
}
function dia_perm(){
    fetch("/report_data?room=" + window.room + "&action=getPermission")
        .then((response) => {
            return response.json();
        })
        .then((data) => {
            $g("permission_board").innerHTML = dorigin;
            if(data["output"]==true){
                data = data["message"];
                rg = [];
                for (const de in data["user"]) {
                    rg.push(de);
                }
                reged("permissionboard_class",[""].concat(rg),"TH");

                for (const de in data["class"]) {
                    newrn = [de];
                    rg.forEach(lem => {
                        if (data["class"][de].includes(lem)){
                            newrn.push("x");
                        }else{
                            newrn.push("");
                        }
                    });
                    reged("permission_board",newrn);
                }
                // gru[]
            // addReported_gac(element);
            }
        })
}
function reportData_gac(){
    fetch("/report_data?room=" + window.room + "&action=getAccess")
        .then((response) => {
            return response.json();
        })
        .then((data) => {
            $g("mcontainer").innerHTML = "";
            addGroup_gac("*");
            if (data["output"] == true){
                for (ik in data["message"]) {
                    element = data["message"][ik];
                    addGroup_gac(element);
                }
            }
            $('.groupbut').on('click',function(){
                $('.active_core').removeClass("active_core");
                this.classList = ['active_core btn btn-light groupbut'];
                window.currentSubGroup = this.textContent.substring(1);
            })
        })
}
function addGroup_gac(elem){
    var node_1 = document.createElement('BUTTON');
    node_1.setAttribute('class', 'btn btn-light groupbut');

    var node_2 = document.createElement('I');
    node_2.setAttribute('class', 'fa-duotone fa-solid fa-folder');
    node_1.appendChild(node_2);

    var node_3 = document.createTextNode((new String(" "+elem)));
    node_1.appendChild(node_3);
    $g("mcontainer").appendChild(node_1);

}
ownergr = {{ownergroup | safe}};
roomjoin = {{roomjoin | safe}};
ownergr.forEach(element => {
    addGroup_style(element,"Owner");
});
roomjoin.forEach(element => {
    addGroup_style(element,"User");
});
if (ownergr.length + roomjoin.length == 0){
    $g("showupe").style.display = "block";
}else{
    $g("showupe").style.display = "none";
}
bind()
function actionex(actionName,substr,refreshRequired=false){
    out = "";
    fetch(strfm("/action?action={0}&{1}",[actionName,substr]))
    .then((data) => {
        return data.json();
    }).then((r) => {
        if (r["output"] == false){
            title = "Error!";
            icon = "error";
            mesg = r["message"];
        }else{
            title = "Success!";
            icon = "success";
            mesg = r["message"];
        }
        if (refreshRequired){
            Swal.fire({
                icon: icon,
                title: title,
                text: mesg,
                willClose: function () {
                    window.location.reload();
                }
            });
        }else{
            Swal.fire({
                icon: icon,
                title: title,
                text: mesg,
            });
        }
    });
}
$("#createRoom_btn").on('click',function(){
    actionex("createRoom",strfm("room={0}",[$g("createRoom").value]),true);
    addGroup_style($g("createRoom").value,"owner");
});


$("#addUser_btn").on('click',function(){
    Swal.fire({
        title: "{{language['bpl.addUser']}}",
        input: "text",
        inputAttributes: {
            autocapitalize: "off"
        },
        showCancelButton: true,
        confirmButtonText: "Ok",
        showLoaderOnConfirm: true,
        preConfirm: async (login) => {
            actionex("addUser",strfm("room={0}&user={1}",[window.room,login]),true);
        },
        allowOutsideClick: () => !Swal.isLoading()
        })
});
$("#removeUser_btn").on('click',function(){
    Swal.fire({
        title: "{{language['bpl.removeUser']}}",
        input: "text",
        inputAttributes: {
            autocapitalize: "off"
        },
        showCancelButton: true,
        confirmButtonText: "Ok",
        showLoaderOnConfirm: true,
        preConfirm: async (login) => {
            actionex("removeUser",strfm("room={0}&user={1}",[window.room,login]),true)
        },
        allowOutsideClick: () => !Swal.isLoading()
        })
})
$("#addGroup_btn").on('click',function(){
    Swal.fire({
        title: "{{language['bpl.addGroup']}}",
        input: "text",
        inputAttributes: {
            autocapitalize: "off"
        },
        showCancelButton: true,
        confirmButtonText: "Ok",
        showLoaderOnConfirm: true,
        preConfirm: async (login) => {
            actionex("addGroup",strfm("room={0}&target={1}&data={2}",[window.room,window.currentSubGroup,login]))
            reportData_gac();
        },
        allowOutsideClick: () => !Swal.isLoading()
    });
})
$("#removeGroup_btn").on('click',function(){
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!"
        }).then((result) => {
        if (result.isConfirmed) {
            currn = window.currentSubGroup.replace("/","_0_tgt")
            if (window.currentSubGroup == "*"){
                Swal.fire({
                    title: "{{language['bpl.warn.are_you_sure']}}",
                    text: "{{language['bpl.warn.remove_group']}}",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Ok"
                    }).then((result) => {
                    if (result.isConfirmed) {
                        actionex("removeGroup",strfm("room={0}&target={1}",[window.room,currn]),true);
                    }
                });
            }else{
                actionex("removeGroup",strfm("room={0}&target={1}",[window.room,currn]));
                reportData_gac()
            }
        }
        });

})
$("#addUserIntoGroup_btn").on('click',function(){
    Swal.fire({
        title: "{{language['bpl.addUserIntoGroup']}}",
        input: "text",
        inputAttributes: {
            autocapitalize: "off"
        },
        showCancelButton: true,
        confirmButtonText: "Look up",
        showLoaderOnConfirm: true,
        preConfirm: async (login) => {
            if (window.currentSubGroup == "*"){
                Swal.fire({
                    title: "{{language['bpl.warn.are_you_sure']}}",
                    text: "{{language['bpl.warn.add_administrator']}}",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Ok"
                    }).then((result) => {
                    if (result.isConfirmed) {
                        actionex("addUserIntoGroup",strfm("room={0}&target={1}&user={2}",[window.room,window.currentSubGroup,login]),true)
                    }
                });
            }else{
                actionex("addUserIntoGroup",strfm("room={0}&target={1}&user={2}",[window.room,window.currentSubGroup,login]),true)
            }
        },
        allowOutsideClick: () => !Swal.isLoading()
        })
});
$("#removeUserFromGroup_btn").on('click',function(){
    Swal.fire({
        title: "{{language['bpl.removeUserFromGroup']}}",
        input: "text",
        inputAttributes: {
            autocapitalize: "off"
        },
        showCancelButton: true,
        confirmButtonText: "Ok",
        showLoaderOnConfirm: true,
        preConfirm: async (login) => {
            actionex("removeUserFromGroup",strfm("room={0}&target={1}&user={2}",[window.room,window.currentSubGroup,login]),true)
        },
        allowOutsideClick: () => !Swal.isLoading()
        })
});
function addMarker_fu(name,value){
    
    var node_1 = document.createElement('DIV');
    node_1.setAttribute('class', 'input-group');

    var node_2 = document.createElement('INPUT');
    node_2.setAttribute('type', 'text');
    node_2.setAttribute('class', 'form-control');
    node_2.setAttribute('placeholder', '{{language["bpl.name"]}}');
    node_2.setAttribute('aria-label', '{{language["bpl.name"]}}');
    if (name){node_2.value = name};
    node_1.appendChild(node_2);

    var node_3 = document.createElement('BUTTON');
    node_3.setAttribute('class', 'btn btn-outline-secondary');
    node_3.setAttribute('type', 'button');
    node_3.addEventListener('click', function(){
        $g("marker_list").removeChild(node_1);
    });
    node_1.appendChild(node_3);

    var node_4 = document.createElement('I');
    node_4.setAttribute('class', 'fa-solid fa-xmark');
    node_3.appendChild(node_4);

    var node_5 = document.createElement('INPUT');
    node_5.setAttribute('type', 'time');
    if (value){$(node_5).val(value)};
    node_5.setAttribute('class', 'btn btn-outline-secondary');
    node_1.appendChild(node_5);
    $g("marker_list").appendChild(node_1);
};
$("#editMarker").on('click',function(){
    Swal.fire({
        title: '{{language["bpl.editMarker"]}}', 
        html: `<div id="marker_list" ><h3>Loading...</h3></div><center><button type="button" class="swal2-confirm swal2-styled" aria-label="" style="display: inline-block;" id="addMarker">{{language["ui.add"]}}</button></center>`,  
        confirmButtonText: "Ok", 
        showCancelButton: true,
        preConfirm: async () => {
            if ($g("marker_list").children.length > 0 && !($g("marker_list").innerHTML == `<h3>{{language["bpl.nomarker"]}}</h3>`)){
                valuerr = "";
                valuename = "";
                for (let index = 0; index < $g("marker_list").children.length; index++) {
                    const element = $g("marker_list").children[index];
                    // Get node_2 and node_5 value
                    valuename += element.querySelectorAll('input')[0].value + "_and_";
                    valuerr += element.querySelectorAll('input')[1].value + "_and_";

                }
                // console.log(valuename,valuerr,Base64.encode(valuerr))
                actionex("setMark",strfm("room={0}&data={1}&name={2}",[window.room,valuerr,Base64.encode(valuename)]))
            }
        }
    });
    fetch("/action?room=" + window.room + "&action=getMark")
        .then((response) => {
            return response.json()
        })
        .then((datax) => {
            $g("marker_list").innerHTML = ``;
            if(datax["output"]==true){
                data = datax["message"];
                r = datax["name"];
                if (data.length > 0){
                    for (let index = 0; index < data.length; index++) {
                        console.log(r[index],data[index]);
                        addMarker_fu(r[index],data[index]);
                    }
                }else{
                    $g("marker_list").innerHTML = `<h3>{{language["bpl.nomarker"]}}</h3>`;

                }
            }
        });
    $("#addMarker").on('click',function(){
        addMarker_fu()
    });

});
$g("fileExcel").onchange = function(){
    const fd = new FormData();
        excl = $g("fileExcel").files[0]
        if (!(excl == undefined)) {
            fd.append('target', window.currentSubGroup);
            fd.append('room', window.room);
            fd.append('excel', excl);
            fetch("/getfile", {
                    method: "POST",
                    // headers: {
                    //     'Content-Type': 'multipart/form-data;charset=utf-8'
                    // },
                    body: fd
                })
                .then(function (res) {
                    return res.json();
                })
                .then(function (data) {
                    if (data["output"] == true) {
                        Swal.fire({
                            title: "{{language['ui.success']}}",
                            text: "{{language['ui.success.import']}}",
                            icon: "success",
                            confirmButtonText: "Ok"
                        })
                    } else {
                        Swal.fire({
                            title: "{{language['ui.error']}}",
                            text: "{{language['ui.failed']}}",
                            icon: "error",
                            confirmButtonText: "Ok"
                        })
                    }
                });
        }
    }

    </script>
<center>
    <small style="color: white;">(C) 2025 By HoangDat</small>
</center>
</body>

<script src="/static/js/uloaderjs.js"></script>

</html>