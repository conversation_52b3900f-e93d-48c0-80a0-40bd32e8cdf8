[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
uvicorn = "*"
aiofiles = "==24.1.0"
cachetools = "==5.5.2"
certifi = "==2025.8.3"
cffi = "==1.17.1"
charset-normalizer = "==3.4.3"
cryptography = "==42.0.8"
et-xmlfile = "==2.0.0"
google-api-core = "==2.25.1"
google-api-python-client = "==2.179.0"
google-auth = "==2.40.3"
google-auth-httplib2 = "==0.2.0"
google-auth-oauthlib = "==1.2.2"
googleapis-common-protos = "==1.70.0"
html5tagger = "==1.3.0"
httplib2 = "==0.22.0"
httptools = "==0.6.4"
idna = "==3.10"
jinja2 = "==3.1.6"
markupsafe = "==3.0.2"
multidict = "==6.6.4"
oauthlib = "==3.3.1"
openpyxl = "==3.1.2"
packaging = "==25.0"
proto-plus = "==1.26.1"
protobuf = "==6.32.0"
pyasn1 = "==0.6.1"
pyasn1-modules = "==0.4.2"
pycparser = "==2.22"
pyparsing = "==3.2.3"
requests = "==2.32.5"
requests-oauthlib = "==2.0.0"
rsa = "==4.9.1"
sanic = "==24.6.0"
sanic-cors = "==2.2.0"
sanic-jinja2 = "==2022.11.11"
sanic-routing = "==23.12.0"
tracerite = "==1.1.3"
typing-extensions = "==4.14.1"
uritemplate = "==4.2.0"
urllib3 = "==2.5.0"
websockets = "==15.0.1"

[dev-packages]

[requires]
python_version = "3.9"
